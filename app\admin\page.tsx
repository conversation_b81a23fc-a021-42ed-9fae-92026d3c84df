"use client"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { Plus, Edit, Trash2 } from "lucide-react"
import { DynamicProductForm, DynamicProductStats, DynamicBulkActions } from "@/components/DynamicAdmin"

interface Product {
  id: string
  type: "account" | "uc" | "hack"
  name: { en: string; ar: string }
  description: { en: string; ar: string }
  price: { egp: number; usd: number }
  image?: string
  images?: string[]
  category?: { en: string; ar: string }
  quantity?: number
  specs?: { [key: string]: { en: string; ar: string } }
  features?: { en: string[]; ar: string[] }
}

export default function AdminPage() {
  const { t } = useLanguage()
  const [activeTab, setActiveTab] = useState<"accounts" | "uc" | "hacks">("accounts")
  const [isAddingProduct, setIsAddingProduct] = useState(false)
  const [editingProduct, setEditingProduct] = useState<string | null>(null)
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])

  // Mock data
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      type: "account",
      name: { en: "Conqueror Account #1", ar: "حساب كونكرر #1" },
      description: { en: "High-tier PUBG account with exclusive skins", ar: "حساب PUBG عالي المستوى مع سكنز حصرية" },
      price: { egp: 1500, usd: 50 },
      images: ["/placeholder.svg?height=200&width=300"],
      specs: {
        rank: { en: "Conqueror", ar: "كونكرر" },
        level: { en: "Level 85", ar: "المستوى 85" },
      },
      features: {
        en: ["Conqueror rank", "50+ legendary skins", "High K/D ratio"],
        ar: ["رتبة كونكرر", "50+ سكن أسطوري", "نسبة قتل عالية"],
      },
    },
    {
      id: "2",
      type: "uc",
      name: { en: "1800 UC Package", ar: "حزمة 1800 يو سي" },
      description: { en: "Perfect for battle pass and skins", ar: "مثالية للباتل باس والسكنز" },
      price: { egp: 450, usd: 15 },
      images: ["/placeholder.svg?height=200&width=300"],
      quantity: 1800,
      features: {
        en: ["Instant delivery", "Safe transaction", "24/7 support"],
        ar: ["تسليم فوري", "معاملة آمنة", "دعم 24/7"],
      },
    },
    {
      id: "3",
      type: "hack",
      name: { en: "ESP Hack Pro", ar: "هاك ESP برو" },
      description: { en: "Advanced ESP with enemy detection", ar: "ESP متقدم مع كشف الأعداء" },
      price: { egp: 300, usd: 10 },
      images: ["/placeholder.svg?height=200&width=300"],
      category: { en: "Vision", ar: "الرؤية" },
      specs: {
        compatibility: { en: "Android 7.0+", ar: "أندرويد 7.0+" },
        detection: { en: "Undetected", ar: "غير قابل للكشف" },
      },
      features: {
        en: ["Enemy ESP", "Item ESP", "Anti-detection", "Regular updates"],
        ar: ["ESP للأعداء", "ESP للعناصر", "مكافحة الكشف", "تحديثات منتظمة"],
      },
    },
  ])

  const filteredProducts = products.filter((product) => product.type === activeTab)

  const handleDeleteSelected = () => {
    if (confirm(`Are you sure you want to delete ${selectedProducts.length} products?`)) {
      setProducts(products.filter((p) => !selectedProducts.includes(p.id)))
      setSelectedProducts([])
    }
  }

  const handleDuplicateSelected = () => {
    const duplicatedProducts = products
      .filter((p) => selectedProducts.includes(p.id))
      .map((p) => ({
        ...p,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: { en: `${p.name.en} (Copy)`, ar: `${p.name.ar} (نسخة)` },
      }))
    setProducts([...products, ...duplicatedProducts])
    setSelectedProducts([])
  }

  const handleExportSelected = () => {
    const selectedData =
      selectedProducts.length > 0 ? products.filter((p) => selectedProducts.includes(p.id)) : products

    const dataStr = JSON.stringify(selectedData, null, 2)
    const dataUri = "data:application/json;charset=utf-8," + encodeURIComponent(dataStr)

    const exportFileDefaultName = `rng-vip-products-${new Date().toISOString().split("T")[0]}.json`

    const linkElement = document.createElement("a")
    linkElement.setAttribute("href", dataUri)
    linkElement.setAttribute("download", exportFileDefaultName)
    linkElement.click()
  }

  const handleImportProducts = (importedProducts: Product[]) => {
    const newProducts = importedProducts.map((p) => ({
      ...p,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    }))
    setProducts([...products, ...newProducts])
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold text-yellow-400 mb-8">{t("dashboard")}</h1>
      <DynamicProductStats products={products} />

      <DynamicBulkActions
        products={filteredProducts}
        selectedProducts={selectedProducts}
        onDeleteSelected={handleDeleteSelected}
        onDuplicateSelected={handleDuplicateSelected}
        onExportSelected={handleExportSelected}
        onImportProducts={handleImportProducts}
      />

      {/* Tabs */}
      <div className="flex space-x-4 mb-8">
        {(["accounts", "uc", "hacks"] as const).map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
              activeTab === tab ? "bg-yellow-500 text-black" : "bg-gray-700 text-gray-300 hover:bg-gray-600"
            }`}
          >
            {t(tab)}
          </button>
        ))}
      </div>

      {/* Add Product Button */}
      {!isAddingProduct && !editingProduct && (
        <button
          onClick={() => setIsAddingProduct(true)}
          className="flex items-center space-x-2 bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-md font-semibold transition-colors mb-6"
        >
          <Plus size={16} />
          <span>{t("addProduct")}</span>
        </button>
      )}

      {/* Add Product Form */}
      {isAddingProduct && (
        <div className="mb-8">
          <DynamicProductForm
            product={undefined}
            productType={activeTab === "accounts" ? "account" : activeTab}
            onSave={(productData: any) => {
              if (productData.id) {
                // Edit existing product
                setProducts(products.map((p) => (p.id === productData.id ? productData : p)))
              } else {
                // Add new product
                const newProduct = { ...productData, id: Date.now().toString() }
                setProducts([...products, newProduct])
              }
              setIsAddingProduct(false)
              setEditingProduct(null)
            }}
            onCancel={() => {
              setIsAddingProduct(false)
              setEditingProduct(null)
            }}
          />
        </div>
      )}

      {/* Products List */}
      <div className="space-y-4">
        {filteredProducts.map((product) => (
          <div key={product.id}>
            {editingProduct === product.id ? (
              <DynamicProductForm
                product={product}
                productType={activeTab === "accounts" ? "account" : activeTab}
                onSave={(productData: any) => {
                  if (productData.id) {
                    // Edit existing product
                    setProducts(products.map((p) => (p.id === productData.id ? productData : p)))
                  } else {
                    // Add new product
                    const newProduct = { ...productData, id: Date.now().toString() }
                    setProducts([...products, newProduct])
                  }
                  setIsAddingProduct(false)
                  setEditingProduct(null)
                }}
                onCancel={() => {
                  setIsAddingProduct(false)
                  setEditingProduct(null)
                }}
              />
            ) : (
              <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20">
                <div className="flex items-start space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedProducts.includes(product.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProducts([...selectedProducts, product.id])
                      } else {
                        setSelectedProducts(selectedProducts.filter((id) => id !== product.id))
                      }
                    }}
                    className="mt-1 w-4 h-4 text-yellow-500 bg-gray-700 border-gray-600 rounded focus:ring-yellow-500"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-2">
                      <h3 className="text-xl font-bold text-yellow-400">{product.name.en}</h3>
                      {product.type === "uc" && product.quantity && (
                        <span className="bg-yellow-500 text-black px-2 py-1 rounded-full text-sm font-bold">
                          {product.quantity} UC
                        </span>
                      )}
                      {product.category && (
                        <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-sm">
                          {product.category.en}
                        </span>
                      )}
                    </div>
                    <p className="text-gray-400 mb-1">{product.name.ar}</p>
                    <p className="text-gray-300 mb-3 line-clamp-2">{product.description.en}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span className="font-semibold text-yellow-400">{product.price.egp} EGP</span>
                      <span className="font-semibold text-green-400">${product.price.usd} USD</span>
                      <span>{product.images?.length || 0} images</span>
                      {product.features && <span>{product.features.en.length} features</span>}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingProduct(product.id)}
                      className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
                      title="Edit Product"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => {
                        if (confirm("Are you sure you want to delete this product?")) {
                          setProducts(products.filter((p) => p.id !== product.id))
                        }
                      }}
                      className="p-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors"
                      title="Delete Product"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
