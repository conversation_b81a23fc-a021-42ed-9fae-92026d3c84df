"use client"

import type React from "react"

import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { Save, X, Trash2, Plus } from "lucide-react"
import Image from "next/image"

interface Product {
  id?: string
  type: "account" | "uc" | "hack"
  name: { en: string; ar: string }
  description: { en: string; ar: string }
  price: { egp: number; usd: number }
  images: string[]
  category?: { en: string; ar: string }
  quantity?: number
  specs?: Record<string, { en: string; ar: string }>
  features?: { en: string[]; ar: string[] }
}

interface ProductFormProps {
  product?: Product
  productType: "account" | "uc" | "hack"
  onSave: (product: Product) => void
  onCancel: () => void
}

export default function ProductForm({ product, productType, onSave, onCancel }: ProductFormProps) {
  const { t } = useLanguage()

  const [formData, setFormData] = useState<Product>({
    type: productType,
    name: { en: product?.name.en || "", ar: product?.name.ar || "" },
    description: { en: product?.description.en || "", ar: product?.description.ar || "" },
    price: { egp: product?.price.egp || 0, usd: product?.price.usd || 0 },
    images: product?.images || ["/placeholder.svg?height=400&width=600"],
    category: { en: product?.category?.en || "", ar: product?.category?.ar || "" },
    quantity: product?.quantity || 0,
    specs: product?.specs || {},
    features: { en: product?.features?.en || [""], ar: product?.features?.ar || [""] },
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.en.trim()) newErrors.nameEn = "English name is required"
    if (!formData.name.ar.trim()) newErrors.nameAr = "Arabic name is required"
    if (!formData.description.en.trim()) newErrors.descEn = "English description is required"
    if (!formData.description.ar.trim()) newErrors.descAr = "Arabic description is required"
    if (formData.price.egp <= 0) newErrors.priceEgp = "EGP price must be greater than 0"
    if (formData.price.usd <= 0) newErrors.priceUsd = "USD price must be greater than 0"

    if (productType === "uc" && formData.quantity! <= 0) {
      newErrors.quantity = "Quantity must be greater than 0"
    }

    if (productType === "hack") {
      if (!formData.category?.en.trim()) newErrors.categoryEn = "English category is required"
      if (!formData.category?.ar.trim()) newErrors.categoryAr = "Arabic category is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSave({ ...formData, id: product?.id })
    }
  }

  const addImage = () => {
    setFormData({
      ...formData,
      images: [...formData.images, "/placeholder.svg?height=400&width=600"],
    })
  }

  const removeImage = (index: number) => {
    setFormData({
      ...formData,
      images: formData.images.filter((_, i) => i !== index),
    })
  }

  const updateImage = (index: number, url: string) => {
    const newImages = [...formData.images]
    newImages[index] = url
    setFormData({ ...formData, images: newImages })
  }

  const addFeature = (lang: "en" | "ar") => {
    setFormData({
      ...formData,
      features: {
        ...formData.features!,
        [lang]: [...formData.features![lang], ""],
      },
    })
  }

  const updateFeature = (lang: "en" | "ar", index: number, value: string) => {
    const newFeatures = { ...formData.features! }
    newFeatures[lang][index] = value
    setFormData({ ...formData, features: newFeatures })
  }

  const removeFeature = (lang: "en" | "ar", index: number) => {
    const newFeatures = { ...formData.features! }
    newFeatures[lang] = newFeatures[lang].filter((_, i) => i !== index)
    setFormData({ ...formData, features: newFeatures })
  }

  const addSpec = () => {
    const key = `spec${Object.keys(formData.specs || {}).length + 1}`
    setFormData({
      ...formData,
      specs: {
        ...formData.specs,
        [key]: { en: "", ar: "" },
      },
    })
  }

  const updateSpec = (key: string, lang: "en" | "ar", value: string) => {
    setFormData({
      ...formData,
      specs: {
        ...formData.specs,
        [key]: {
          ...formData.specs![key],
          [lang]: value,
        },
      },
    })
  }

  const removeSpec = (key: string) => {
    const newSpecs = { ...formData.specs }
    delete newSpecs[key]
    setFormData({ ...formData, specs: newSpecs })
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-yellow-500/20 max-w-6xl mx-auto">
      <h3 className="text-2xl font-bold text-yellow-400 mb-6">
        {product ? t("editProduct") : t("addProduct")} - {productType.toUpperCase()}
      </h3>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Basic Information</h4>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">{t("productName")} (English) *</label>
              <input
                type="text"
                value={formData.name.en}
                onChange={(e) => setFormData({ ...formData, name: { ...formData.name, en: e.target.value } })}
                className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                  errors.nameEn ? "border-red-500" : "border-gray-600"
                }`}
                placeholder="Enter product name in English"
              />
              {errors.nameEn && <p className="text-red-400 text-sm mt-1">{errors.nameEn}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">{t("productName")} (Arabic) *</label>
              <input
                type="text"
                value={formData.name.ar}
                onChange={(e) => setFormData({ ...formData, name: { ...formData.name, ar: e.target.value } })}
                className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                  errors.nameAr ? "border-red-500" : "border-gray-600"
                }`}
                placeholder="أدخل اسم المنتج بالعربية"
              />
              {errors.nameAr && <p className="text-red-400 text-sm mt-1">{errors.nameAr}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t("productDescription")} (English) *
              </label>
              <textarea
                value={formData.description.en}
                onChange={(e) =>
                  setFormData({ ...formData, description: { ...formData.description, en: e.target.value } })
                }
                className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                  errors.descEn ? "border-red-500" : "border-gray-600"
                }`}
                rows={4}
                placeholder="Enter product description in English"
              />
              {errors.descEn && <p className="text-red-400 text-sm mt-1">{errors.descEn}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t("productDescription")} (Arabic) *
              </label>
              <textarea
                value={formData.description.ar}
                onChange={(e) =>
                  setFormData({ ...formData, description: { ...formData.description, ar: e.target.value } })
                }
                className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                  errors.descAr ? "border-red-500" : "border-gray-600"
                }`}
                rows={4}
                placeholder="أدخل وصف المنتج بالعربية"
              />
              {errors.descAr && <p className="text-red-400 text-sm mt-1">{errors.descAr}</p>}
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Pricing & Details</h4>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">{t("productPrice")} (EGP) *</label>
                <input
                  type="number"
                  value={formData.price.egp}
                  onChange={(e) =>
                    setFormData({ ...formData, price: { ...formData.price, egp: Number(e.target.value) } })
                  }
                  className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                    errors.priceEgp ? "border-red-500" : "border-gray-600"
                  }`}
                  min="0"
                />
                {errors.priceEgp && <p className="text-red-400 text-sm mt-1">{errors.priceEgp}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">{t("productPrice")} (USD) *</label>
                <input
                  type="number"
                  value={formData.price.usd}
                  onChange={(e) =>
                    setFormData({ ...formData, price: { ...formData.price, usd: Number(e.target.value) } })
                  }
                  className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                    errors.priceUsd ? "border-red-500" : "border-gray-600"
                  }`}
                  min="0"
                />
                {errors.priceUsd && <p className="text-red-400 text-sm mt-1">{errors.priceUsd}</p>}
              </div>
            </div>

            {productType === "uc" && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">{t("quantity")} *</label>
                <input
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}
                  className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                    errors.quantity ? "border-red-500" : "border-gray-600"
                  }`}
                  min="0"
                />
                {errors.quantity && <p className="text-red-400 text-sm mt-1">{errors.quantity}</p>}
              </div>
            )}

            {productType === "hack" && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">{t("category")} (English) *</label>
                  <input
                    type="text"
                    value={formData.category?.en || ""}
                    onChange={(e) =>
                      setFormData({ ...formData, category: { ...formData.category!, en: e.target.value } })
                    }
                    className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                      errors.categoryEn ? "border-red-500" : "border-gray-600"
                    }`}
                    placeholder="e.g., Vision, Aiming, Movement"
                  />
                  {errors.categoryEn && <p className="text-red-400 text-sm mt-1">{errors.categoryEn}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">{t("category")} (Arabic) *</label>
                  <input
                    type="text"
                    value={formData.category?.ar || ""}
                    onChange={(e) =>
                      setFormData({ ...formData, category: { ...formData.category!, ar: e.target.value } })
                    }
                    className={`w-full bg-gray-700 border rounded-md px-3 py-2 text-white ${
                      errors.categoryAr ? "border-red-500" : "border-gray-600"
                    }`}
                    placeholder="مثال: الرؤية، التصويب، الحركة"
                  />
                  {errors.categoryAr && <p className="text-red-400 text-sm mt-1">{errors.categoryAr}</p>}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Images */}
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">Product Images</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {formData.images.map((image, index) => (
              <div key={index} className="relative bg-gray-700 rounded-lg p-4">
                <div className="aspect-video bg-gray-600 rounded-lg mb-3 overflow-hidden">
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`Product image ${index + 1}`}
                    width={300}
                    height={200}
                    className="object-cover w-full h-full"
                  />
                </div>
                <input
                  type="text"
                  value={image}
                  onChange={(e) => updateImage(index, e.target.value)}
                  className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white text-sm mb-2"
                  placeholder="Image URL"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="w-full flex items-center justify-center space-x-2 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md text-sm transition-colors"
                >
                  <Trash2 size={16} />
                  <span>Remove</span>
                </button>
              </div>
            ))}
            <button
              type="button"
              onClick={addImage}
              className="aspect-video bg-gray-700 border-2 border-dashed border-gray-500 rounded-lg flex items-center justify-center hover:border-yellow-500 transition-colors"
            >
              <div className="text-center">
                <Plus size={32} className="text-gray-400 mx-auto mb-2" />
                <span className="text-gray-400">Add Image</span>
              </div>
            </button>
          </div>
        </div>

        {/* Features */}
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">Product Features</h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Features (English)</label>
              <div className="space-y-2">
                {formData.features?.en.map((feature, index) => (
                  <div key={index} className="flex space-x-2">
                    <input
                      type="text"
                      value={feature}
                      onChange={(e) => updateFeature("en", index, e.target.value)}
                      className="flex-1 bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white"
                      placeholder="Enter feature"
                    />
                    <button
                      type="button"
                      onClick={() => removeFeature("en", index)}
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addFeature("en")}
                  className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md transition-colors"
                >
                  <Plus size={16} />
                  <span>Add Feature</span>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Features (Arabic)</label>
              <div className="space-y-2">
                {formData.features?.ar.map((feature, index) => (
                  <div key={index} className="flex space-x-2">
                    <input
                      type="text"
                      value={feature}
                      onChange={(e) => updateFeature("ar", index, e.target.value)}
                      className="flex-1 bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white"
                      placeholder="أدخل الميزة"
                    />
                    <button
                      type="button"
                      onClick={() => removeFeature("ar", index)}
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addFeature("ar")}
                  className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md transition-colors"
                >
                  <Plus size={16} />
                  <span>إضافة ميزة</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Specifications */}
        {(productType === "account" || productType === "hack") && (
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Specifications</h4>
            <div className="space-y-4">
              {Object.entries(formData.specs || {}).map(([key, spec]) => (
                <div key={key} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-700 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Spec Key</label>
                    <input
                      type="text"
                      value={key}
                      readOnly
                      className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Value (English)</label>
                    <input
                      type="text"
                      value={spec.en}
                      onChange={(e) => updateSpec(key, "en", e.target.value)}
                      className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white"
                      placeholder="Enter value in English"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-300 mb-2">Value (Arabic)</label>
                      <input
                        type="text"
                        value={spec.ar}
                        onChange={(e) => updateSpec(key, "ar", e.target.value)}
                        className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white"
                        placeholder="أدخل القيمة بالعربية"
                      />
                    </div>
                    <div className="flex items-end">
                      <button
                        type="button"
                        onClick={() => removeSpec(key)}
                        className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md transition-colors"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={addSpec}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                <Plus size={16} />
                <span>Add Specification</span>
              </button>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex space-x-4 pt-6 border-t border-gray-700">
          <button
            type="submit"
            className="flex items-center space-x-2 bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-3 rounded-md font-semibold transition-colors"
          >
            <Save size={20} />
            <span>{t("save")}</span>
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md font-semibold transition-colors"
          >
            <X size={20} />
            <span>{t("cancel")}</span>
          </button>
        </div>
      </form>
    </div>
  )
}
