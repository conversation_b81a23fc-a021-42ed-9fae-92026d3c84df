"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { doc, getDoc, setDoc, getFirestore } from 'firebase/firestore';
import { auth } from '@/lib/firebase';
import SimpleLoader from '@/components/SimpleLoader';

interface UserData {
  displayName: string;
  email: string;
  photoURL: string;
  createdAt: string;
  lastLoginAt: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  country?: string;
  city?: string;
  dateOfBirth?: string;
  bio?: string;
}

interface AuthContextType {
  user: User | null;
  userData: UserData | null;
  loading: boolean;
  updateUserData: (data: Partial<UserData>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({ 
  user: null, 
  userData: null, 
  loading: true,
  updateUserData: async () => {} 
});

const db = getFirestore();

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  // Function to fetch or create user data
  const fetchOrCreateUserData = async (user: User) => {
    const userRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      // Update last login
      const data = userDoc.data() as UserData;
      await setDoc(userRef, { ...data, lastLoginAt: new Date().toISOString() }, { merge: true });
      setUserData(data);
    } else {
      // Create new user data
      const newUserData: UserData = {
        displayName: user.displayName || user.email?.split('@')[0] || '',
        email: user.email || '',
        photoURL: user.photoURL || '',
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      };
      await setDoc(userRef, newUserData);
      setUserData(newUserData);
    }
  };

  // Function to update user data
  const updateUserData = async (data: Partial<UserData>) => {
    if (!user) return;

    const userRef = doc(db, 'users', user.uid);
    await setDoc(userRef, { ...data, lastLoginAt: new Date().toISOString() }, { merge: true });
    setUserData(prev => prev ? { ...prev, ...data } : null);
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      if (user) {
        await fetchOrCreateUserData(user);
      } else {
        setUserData(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    return <SimpleLoader />;
  }

  return (
    <AuthContext.Provider value={{ user, userData, loading, updateUserData }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext); 